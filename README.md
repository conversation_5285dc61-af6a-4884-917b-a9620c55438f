# document-writer

一个基于 ConnectOnion Agent 的自动化 README 生成工具。它可以扫描本地文件或目录，利用预定义的 Prompt 和工具集，自动产出高质量的项目级 README.md，并可渲染成静态 HTML 页面，方便快速对外交付或展示。

## 特性

- 支持“目录模式”与“文件模式”  
  • 目录模式：通过 `read_directory_tree` 汇总代表性文件  
  • 文件模式：通过 `read_text_file` 读取单个文件  
- 内置三大工具函数  
  • read_text_file – 按字符上限读取文本文件  
  • read_directory_tree – 扫描目录并选取优先级文件  
  • detect_language_from_extension – 根据扩展名推断语言生态  
- 多级 Prompt 模板  
  • SYSTEM_PROMPT.md – 系统级指令  
  • CLI_USER_PROMPT_TMPL_DIR.md – 目录模式用户指令模板  
  • CLI_USER_PROMPT_TMPL_FILE.md – 文件模式用户指令模板  
- 生成 Markdown（README.generated.md）与静态 HTML (`index.html`)  
- 灵活可配置  
  • `--max-files`、`--max-chars-per-file` 控制扫描深度和颗粒度  
  • `--include` / `--exclude` 自定义 glob 模式  

---

## 目录结构

```text
.
├── README.md                            # 本文件：项目自述
├── .co/
│   └── docs/
│       └── config.toml                  # ConnectOnion 项目配置
├── .env                                 # 环境变量示例：API Key
├── agent.py                             # CLI 入口：驱动 Agent 执行
├── render.py                            # Markdown → HTML 渲染工具
├── prompts/
│   ├── SYSTEM_PROMPT.md                 # Agent 系统级 Prompt
│   ├── CLI_USER_PROMPT_TMPL_DIR.md      # 目录模式下的用户 Prompt 模板
│   └── CLI_USER_PROMPT_TMPL_FILE .md    # 文件模式下的用户 Prompt 模板
└── tools/
    ├── fs.py                            # 文件扫描与读取工具
    ├── lang.py                          # 根据扩展名推断语言生态
    └── __init__.py
```

---

## 环境要求

- Python 3.7 及以上  
- 推荐依赖（可按需安装）：  
  ```bash
  pip install connectonion python-dotenv markdown
  ```

---

## 安装与快速开始

1. 克隆仓库并进入目录  
   ```bash
   git clone <仓库地址>
   cd document-writer
   ```
2. 准备环境变量  
   在根目录创建 `.env`，并填入你的 API Key：  
   ```dotenv
   OPENONION_API_KEY=<your_openonion_api_key>
   ```
3. 生成 README  
   - 整个目录模式：  
     ```bash
     python agent.py . --max-files 100 --max-chars-per-file 20000
     ```
     生成 `README.generated.md` 和静态页面 `index.html`。  
   - 单文件模式：  
     ```bash
     python agent.py path/to/your/file.py
     ```

---

## 配置说明

- `--max-files`：目录模式下扫描的最大文件数，默认 120  
- `--max-chars-per-file`：单文件读取字符上限，默认 20000  
- `--include` / `--exclude`：强制包含／排除的 glob 模式  
- `--model`：可选，指定 ConnectOnion 模型名称  

---

## 使用示例

```bash
# 目录模式：只扫描 Markdown 文件，排除 __pycache__
python agent.py ./my-project \
  --max-files 50 \
  --max-chars-per-file 10000 \
  --include "**/*.md" \
  --exclude "**/__pycache__/**"

# 文件模式
python agent.py ./scripts/deploy.sh
```

生成的 `README.generated.md` 会包含项目简介、目录结构、安装运行指南、开发流程等章节。

---

## 开发与测试

- 核心模块  
  - `agent.py`：CLI 参数解析 & Agent 构建  
  - `tools/fs.py`、`tools/lang.py`：文件扫描、读取与语言检测  
  - `render.py`：Markdown → HTML 渲染  
  - `prompts/`：系统级与用户级 Prompt 模板  
- 建议在虚拟环境中开发，使用 `pytest` 或自定义脚本做单元测试  
- 可扩展点  
  - 支持更多文件类型或文档格式  
  - 丰富 Prompt 模板，调整生成风格  

---

## 架构概览

1. **CLI 层（agent.py）**  
   - 解析命令行参数  
   - 判断目标是文件还是目录  
   - 载入对应的用户 Prompt 模板，并拼接 `[TOOL_HINTS]` JSON  
   - 调用 ConnectOnion Agent 生成 Markdown 文本  
   - 写入 `README.generated.md` 并调用 `render.py` 生成静态 HTML  
2. **工具层（tools/）**  
   - `fs.py`：目录遍历、文件探针、内容读取（支持截断）  
   - `lang.py`：根据扩展名推断语言生态  
3. **Prompt 层（prompts/）**  
   - 系统级 Prompt 定义整体策略  
   - 用户级 Prompt 模板指导生成逻辑  
4. **渲染层（render.py）**  
   - 依赖 `markdown` 包（可选），支持代码高亮、目录生成  
   - 输出 GitHub 样式静态 HTML 页面  

---

## 致谢

- 感谢 ConnectOnion 团队与开源社区的支持  
- 感谢 Python 生态中的所有贡献者  

---

## 限制与后续补全

- 当前仅处理常见文本文件格式，二进制或特殊文档（PDF、图片）未纳入  
- 如需更细粒度或更大文件覆盖，可调整：  
  - `--max-files`、`--max-chars-per-file`  
  - `--include` / `--exclude` glob 模式  
- 如需支持更复杂文档，可考虑引入 OCR、图像解析或专用解析插件。