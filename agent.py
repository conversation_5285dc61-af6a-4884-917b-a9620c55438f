from __future__ import annotations
import argparse
import fnmatch
import json
import mimetypes
import os
from dataclasses import dataclass
from typing import Dict, Any, Optional, List
from pathlib import Path
from tools import read_text_file, read_directory_tree, detect_language_from_extension
from render import render_site, MARKDOWN_AVAILABLE



# --- ConnectOnion imports ---
# pip install connectonion
from connectonion import Agent
from dotenv import load_dotenv
load_dotenv()


# ========== Paths & Prompts ==========
HERE = Path(__file__).parent.resolve()
PROMPTS = HERE / "prompts"
SYSTEM_PROMPT_FILE = PROMPTS / "SYSTEM_PROMPT.md"
TMPL_FILE = PROMPTS / "CLI_USER_PROMPT_TMPL_FILE.md"
TMPL_DIR  = PROMPTS / "CLI_USER_PROMPT_TMPL_DIR.md"


model = os.getenv("MODEL", "co/o4-mini")
api_key = os.getenv("CO_API_KEY") or os.getenv("OPENONION_API_KEY")


# ========== Agent ==========
def build_agent(model=model, api_key=api_key) -> Agent:
    # Different ConnectOnion versions take different __init__ signatures.
    # Prefer keyword args with an explicit `name`, and fall back to positional if needed.
    try:
        return Agent(
            name="readme-writer",
            system_prompt= SYSTEM_PROMPT_FILE.read_text(encoding="utf-8"),
            tools=[read_text_file, read_directory_tree, detect_language_from_extension],
            max_iterations=8,
            model=model,           # ← 关键
            api_key=api_key,       # 
        )
    except TypeError:
        # Older signature: Agent(name, system_prompt, tools, **kwargs)
        return Agent(
            "readme-writer",
            SYSTEM_PROMPT_FILE.read_text(encoding="utf-8"),
            [read_text_file, read_directory_tree, detect_language_from_extension],
            max_iterations=8,
            model=model,           # ← 关键
            api_key=api_key,       # 
        )


# ========== CLI ==========
def main():
    parser = argparse.ArgumentParser(description="Generate README from a local file or an entire folder using ConnectOnion agent")
    parser.add_argument("path", help="Path to the local file or directory")
    parser.add_argument("--max-files", type=int, default=120, help="Directory mode: max number of files to sample")
    parser.add_argument("--max-chars-per-file", type=int, default=20_000, help="Directory mode: per-file char cap")
    parser.add_argument("--include", nargs="*", default=[], help="Glob patterns to force-include (e.g. '**/*.py' 'README.md')")
    parser.add_argument("--exclude", nargs="*", default=[], help="Glob patterns to exclude in addition to defaults")
    parser.add_argument("--model", type=str, default=None, help="(Optional) Model name if your ConnectOnion setup supports selecting models")
    args = parser.parse_args()

    target = os.path.abspath(args.path)
    is_dir = os.path.isdir(target)
    is_file = os.path.isfile(target)
    if not (is_dir or is_file):
        raise SystemExit(f"❌ Path not found: {target}")

    agent = build_agent(model=model, api_key=api_key)

    # Build the user prompt according to mode
    if is_dir:
        user_prompt = TMPL_DIR.read_text(encoding="utf-8").format(path=target)
        # Provide tool-call hints as context the agent may echo
        tool_hints = {
            "root": target,
            "max_files": args.max_files,
            "max_chars_per_file": args.max_chars_per_file,
            "include": args.include,
            "exclude": args.exclude,
        }
        # Some ConnectOnion runtimes let you pass "memory" or extra context; we simply append JSON hint
        user_prompt += "[TOOL_HINTS]" + json.dumps(tool_hints, ensure_ascii=False)
    else:
        user_prompt = TMPL_FILE.read_text(encoding="utf-8").format(path=target)

    # Try several common agent run APIs for compatibility
    result: Optional[str] = None
    try:
        result = agent.input(user_prompt)  # type: ignore[attr-defined]
    except Exception:
        try:
            result = agent.run(user_prompt)  # type: ignore[attr-defined]
        except Exception:
            result = agent(user_prompt)  # type: ignore[call-arg]

    if not isinstance(result, str):
        try:
            result = result.get("output")  # type: ignore[assignment]
        except Exception:
            pass

    if not isinstance(result, str) or len(result.strip()) == 0:
        raise SystemExit("❌ Agent returned empty result. Check your ConnectOnion setup and logs.")

    # Write README.md to the target directory (folder mode) or alongside the file (file mode)
    out_dir = target if is_dir else os.path.dirname(target)
    out_path = os.path.join(out_dir, "README.generated.md")
    with open(out_path, "w", encoding="utf-8") as f:
        f.write(result)

    print("✅ README.generated.md 已生成：" + out_path)

    # --- Render to a static site (index.html) ---
    html_path = render_site(result, out_dir, title=os.path.basename(target))
    print("🌐 静态页面已生成：" + html_path)
    if not MARKDOWN_AVAILABLE:
        print("(提示) 若要更好的 Markdown 渲染与代码高亮，建议安装: pip install markdown")



if __name__ == "__main__":
    main()