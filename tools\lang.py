def detect_language_from_extension(ext: str) -> str:
    ext = (ext or "").lower()
    mapping = {
        ".py":"Python",".ipynb":"<PERSON>py<PERSON>",".js":"JavaScript",".ts":"TypeScript",
        ".jsx":"React",".tsx":"React+TS",".java":"Java",".go":"Go",".rs":"Rust",
        ".cpp":"C++",".c":"C",".md":"Markdown",".sh":"Shell",".yaml":"YAML",
        ".yml":"YAML",".toml":"TOML",".json":"JSON",
    }
    return mapping.get(ext, "Unknown")
