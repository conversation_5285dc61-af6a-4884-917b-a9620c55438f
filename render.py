# render.py
from __future__ import annotations
import os

# ========== Markdown parser (optional) ==========
try:
    import markdown as _md
except Exception:
    _md = None
MARKDOWN_AVAILABLE = _md is not None

HTML_TEMPLATE = """<!doctype html>
<html lang="zh">
<head>
  <meta charset="utf-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1" />
  <title>{title}</title>
  <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/github-markdown-css@5/github-markdown.min.css">
  <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/highlight.js@11.9.0/styles/github.min.css">
  <style>
    body {{ margin:0; padding:0; }}
    .container {{ max-width: 900px; margin: 0 auto; padding: 2rem; }}
    .markdown-body {{ box-sizing: border-box; min-width: 200px; }}
  </style>
  {extra_head}
</head>
<body>
  <div class="container">
    <article class="markdown-body">{content}</article>
  </div>
  <script src="https://cdn.jsdelivr.net/npm/highlight.js@11.9.0/lib/highlight.min.js"></script>
  <script>document.querySelectorAll('pre code').forEach((el)=>window.hljs && hljs.highlightElement(el));</script>
</body>
</html>"""

def _md_to_html(md_text: str) -> str:
    """
    将 Markdown 转为 HTML：
    - 优先使用 'markdown' 包（更完整）
    - 缺失时走极简降级渲染（标题/段落 + HTML 转义）
    """
    if _md is not None:
        # 避免和 highlight.js 冲突，这里不启用 "codehilite"
        return _md.markdown(
            md_text,
            extensions=[
                "fenced_code",   # ``` 代码块
                "tables",        # 表格
                "toc",           # 目录 (会插入带 id 的标题锚点)
                "sane_lists",
                "smarty",
            ],
            output_format="html5",
        )
    # 简易降级：安全转义 + h1..h6 + 段落
    import html
    out_lines = []
    for line in md_text.splitlines():
        if line.startswith("#"):
            h = len(line) - len(line.lstrip("#"))
            h = max(1, min(h, 6))
            out_lines.append(f"<h{h}>"+html.escape(line[h:].strip())+f"</h{h}>")
        else:
            out_lines.append("<p>"+html.escape(line)+"</p>")
    return "\n".join(out_lines)

def render_html_string(markdown_text: str, title: str = "Project README", extra_head: str = "") -> str:
    """把 Markdown 渲染为完整 HTML 字符串（不落盘）。"""
    html_content = _md_to_html(markdown_text)
    return HTML_TEMPLATE.format(title=title, content=html_content, extra_head=extra_head)

def render_site(markdown_text: str, out_dir: str, title: str = "Project README", extra_head: str = "") -> str:
    """
    把 Markdown 渲染为完整 HTML 并写入 out_dir/index.html，返回文件路径。
    - 若 out_dir 不存在，会自动创建。
    - extra_head 可注入额外 <meta>、样式或脚本（比如 MathJax、Mermaid）。
    """
    os.makedirs(out_dir, exist_ok=True)
    html_doc = render_html_string(markdown_text, title=title, extra_head=extra_head)
    out_html = os.path.join(out_dir, "index.html")
    with open(out_html, "w", encoding="utf-8") as f:
        f.write(html_doc)
    return out_html
