<!doctype html>
<html lang="zh">
<head>
  <meta charset="utf-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1" />
  <title>document-writer</title>
  <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/github-markdown-css@5/github-markdown.min.css">
  <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/highlight.js@11.9.0/styles/github.min.css">
  <style>
    body { margin:0; padding:0; }
    .container { max-width: 900px; margin: 0 auto; padding: 2rem; }
    .markdown-body { box-sizing: border-box; min-width: 200px; }
  </style>
  
</head>
<body>
  <div class="container">
    <article class="markdown-body"><h1>document-writer</h1>
<p></p>
<p>一个基于 ConnectOnion Agent 的自动化 README 生成工具。它可以扫描本地文件或目录，利用预定义的 Prompt 和工具集，自动产出高质量的项目级 `README.generated.md`，并可渲染成静态 HTML 页面，方便快速对外交付或展示。</p>
<p></p>
<h2>特性</h2>
<p></p>
<p>- 支持“目录模式”与“文件模式”  </p>
<p>  - 目录模式：通过 `read_directory_tree` 汇总代表性文件  </p>
<p>  - 文件模式：通过 `read_text_file` 读取单个文件  </p>
<p>- 内置三大工具函数  </p>
<p>  - `read_text_file` – 按字符上限读取文本文件  </p>
<p>  - `read_directory_tree` – 扫描目录并选取优先级文件  </p>
<p>  - `detect_language_from_extension` – 根据扩展名推断语言生态  </p>
<p>- 多级 Prompt 模板  </p>
<p>  - `prompts/SYSTEM_PROMPT.md` – 系统级指令  </p>
<p>  - `prompts/CLI_USER_PROMPT_TMPL_DIR.md` – 目录模式用户指令模板  </p>
<p>  - `prompts/CLI_USER_PROMPT_TMPL_FILE .md` – 文件模式用户指令模板  </p>
<p>- 生成 Markdown（`README.generated.md`）与静态 HTML (`index.html`)  </p>
<p>- 灵活可配置  </p>
<p>  - `--max-files`、`--max-chars-per-file` 控制扫描深度和颗粒度  </p>
<p>  - `--include` / `--exclude` 自定义 glob 模式  </p>
<p></p>
<h2>目录结构</h2>
<p></p>
<p>```text</p>
<p>.</p>
<p>├── README.md                             # 本文件：项目自述</p>
<p>├── .co/</p>
<p>│   └── docs/</p>
<p>│       └── config.toml                   # ConnectOnion 项目配置</p>
<p>├── .env                                  # 环境变量示例：API Key</p>
<p>├── agent.py                              # CLI 入口：驱动 Agent 执行</p>
<p>├── render.py                             # Markdown → HTML 渲染工具</p>
<p>├── prompts/</p>
<p>│   ├── SYSTEM_PROMPT.md                  # Agent 系统级 Prompt</p>
<p>│   ├── CLI_USER_PROMPT_TMPL_DIR.md       # 目录模式下的用户 Prompt 模板</p>
<p>│   └── CLI_USER_PROMPT_TMPL_FILE .md     # 文件模式下的用户 Prompt 模板</p>
<p>└── tools/</p>
<p>    ├── fs.py                             # 文件扫描与读取工具</p>
<p>    └── lang.py                           # 根据扩展名推断语言生态</p>
<p>```</p>
<p></p>
<h2>环境要求</h2>
<p></p>
<p>- Python 3.7 及以上  </p>
<p>- 推荐依赖（可按需安装）：</p>
<p>  ```bash</p>
<p>  pip install connectonion python-dotenv markdown</p>
<p>  ```</p>
<p></p>
<h2>安装与快速开始</h2>
<p></p>
<p>1. 克隆仓库并进入目录  </p>
<p>   ```bash</p>
<p>   git clone &lt;仓库地址&gt;</p>
<p>   cd document-writer</p>
<p>   ```</p>
<p>2. 安装依赖  </p>
<p>   ```bash</p>
<p>   pip install connectonion python-dotenv markdown</p>
<p>   ```</p>
<p>3. 准备环境变量  </p>
<p>   在根目录创建 `.env`，并填入你的 API Key：</p>
<p>   ```dotenv</p>
<p>   OPENONION_API_KEY=&lt;your_openonion_api_key&gt;</p>
<p>   ```</p>
<p>4. 生成 README  </p>
<p>   - **目录模式**：  </p>
<p>     ```bash</p>
<p>     python agent.py . --max-files 100 --max-chars-per-file 20000</p>
<p>     ```</p>
<p>     生成 `README.generated.md` 和静态页面 `index.html`。  </p>
<p>   - **文件模式**：  </p>
<p>     ```bash</p>
<p>     python agent.py path/to/your/file.py</p>
<p>     ```</p>
<p></p>
<h2>配置说明</h2>
<p></p>
<p>- `--max-files`：目录模式下扫描的最大文件数，默认 120  </p>
<p>- `--max-chars-per-file`：单文件读取字符上限，默认 20000  </p>
<p>- `--include` / `--exclude`：强制包含／排除的 glob 模式  </p>
<p>- `--model`：可选，指定 ConnectOnion 模型名称（如 `gpt-4o-mini`）</p>
<p></p>
<h2>使用示例</h2>
<p></p>
<p>```bash</p>
<h1>仅扫描 Markdown 文件，排除 __pycache__</h1>
<p>python agent.py ./my-project \</p>
<p>  --max-files 50 \</p>
<p>  --max-chars-per-file 10000 \</p>
<p>  --include &quot;**/*.md&quot; \</p>
<p>  --exclude &quot;**/__pycache__/**&quot;</p>
<p></p>
<h1>单文件模式</h1>
<p>python agent.py ./scripts/deploy.sh</p>
<p>```</p>
<p></p>
<p>生成的 `README.generated.md` 会包含项目简介、目录结构、安装运行指南、开发流程等章节。</p>
<p></p>
<h2>开发与测试</h2>
<p></p>
<p>- 核心模块  </p>
<p>  - `agent.py`：CLI 参数解析 &amp; Agent 构建  </p>
<p>  - `tools/fs.py`、`tools/lang.py`：文件扫描、读取与语言检测  </p>
<p>  - `render.py`：Markdown → HTML 渲染  </p>
<p>  - `prompts/`：系统级与用户级 Prompt 模板  </p>
<p>- 建议在虚拟环境中开发，使用 `pytest` 或自定义脚本进行单元测试  </p>
<p>- 可扩展点  </p>
<p>  - 支持更多文件格式或文档类型（如 PDF、Office 等）  </p>
<p>  - 丰富 Prompt 模板，调整生成风格</p>
<p></p>
<h2>架构概览</h2>
<p></p>
<p>1. **CLI 层（agent.py）**  </p>
<p>   - 解析命令行参数  </p>
<p>   - 判断目标是文件还是目录  </p>
<p>   - 载入对应的用户 Prompt 模板，并拼接 `[TOOL_HINTS]` JSON  </p>
<p>   - 调用 ConnectOnion Agent 生成 Markdown 文本  </p>
<p>   - 写入 `README.generated.md` 并调用 `render.py` 生成静态 HTML  </p>
<p>2. **工具层（tools/）**  </p>
<p>   - `fs.py`：目录遍历、文件探针、内容读取（支持截断）  </p>
<p>   - `lang.py`：根据扩展名推断语言生态  </p>
<p>3. **Prompt 层（prompts/）**  </p>
<p>   - 系统级 Prompt 定义整体策略  </p>
<p>   - 用户级 Prompt 模板指导生成逻辑  </p>
<p>4. **渲染层（render.py）**  </p>
<p>   - 依赖 `markdown` 包（可选），支持代码高亮、目录生成  </p>
<p>   - 输出 GitHub 样式静态 HTML 页面</p>
<p></p>
<h2>致谢</h2>
<p></p>
<p>- 感谢 ConnectOnion 团队与开源社区的支持  </p>
<p>- 感谢 Python 生态中的所有贡献者  </p>
<p></p>
<h2>限制与后续补全</h2>
<p></p>
<p>- 当前仅处理常见文本文件格式，二进制或特殊文档（如 PDF、图片、Office 文件）未纳入扫描。  </p>
<p>- 部分文件（如 `agent.py`、`tools/fs.py`）因 `--max-chars-per-file` 默认值（5000 字符）被截断，可能遗漏关键实现细节。  </p>
<p>- 如需更完整覆盖，可：</p>
<p>  - 提高 `--max-files` 与 `--max-chars-per-file` 上限  </p>
<p>  - 增加或调整 `--include` / `--exclude` 的 glob 模式  </p>
<p>- 若需支持更复杂文档结构，可考虑引入专用解析插件或 OCR、图像识别等方案。</p></article>
  </div>
  <script src="https://cdn.jsdelivr.net/npm/highlight.js@11.9.0/lib/highlight.min.js"></script>
  <script>document.querySelectorAll('pre code').forEach((el)=>window.hljs && hljs.highlightElement(el));</script>
</body>
</html>