from __future__ import annotations
import fnmatch, mimetypes, os
from dataclasses import dataclass
from typing import Dict, Any, Optional, List, Tuple

@dataclass
class FileInfo:
    path: str
    rel: str
    name: str
    ext: str
    size_bytes: int
    mime: str

TEXTY_EXTS = {".py", ".ipynb", ".md", ".txt", ".json", ".toml", ".ini", ".yaml", ".yml",
              ".js", ".ts", ".tsx", ".jsx", ".c", ".h", ".cpp", ".hpp", ".java", ".go", ".rs", ".sh"}
BINARY_LIKE_EXTS = {".png", ".jpg", ".jpeg", ".gif", ".webp", ".pdf", ".zip", ".tar", ".gz",
                    ".xz", ".mp3", ".mp4", ".mov", ".ogg", ".wav", ".ttf", ".otf", ".woff", ".woff2"}
DEFAULT_EXCLUDES = [".git", "node_modules", ".venv", "venv", "__pycache__", "dist", "build",
                    "coverage", ".mypy_cache", ".idea", ".vscode"]

def _probe_file(abs_path: str, root: Optional[str] = None) -> FileInfo:
    st = os.stat(abs_path)
    name = os.path.basename(abs_path)
    ext = os.path.splitext(name)[1].lower()
    mime, _ = mimetypes.guess_type(abs_path)
    rel = os.path.relpath(abs_path, root) if root else name
    return FileInfo(
        path=os.path.abspath(abs_path),
        rel=rel, name=name, ext=ext or "",
        size_bytes=st.st_size, mime=mime or "application/octet-stream",
    )




def read_text_file(path: str, max_chars: int = 120_000) -> Dict[str, Any]:
    if not os.path.exists(path):
        raise FileNotFoundError(f"File not found: {path}")
    info = _probe_file(path)
    note = ""
    if info.mime and not info.mime.startswith("text") and info.ext not in TEXTY_EXTS:
        note = f"Warning: non-text MIME detected ({info.mime}). Attempting to read as UTF-8."
    raw = open(path, "rb").read()
    try:
        text = raw.decode("utf-8")
    except UnicodeDecodeError:
        text = raw.decode("latin-1")
        note = (note + "" if note else "") + "Fallback decoding with latin-1."
    truncated = False
    if len(text) > max_chars:
        text = text[:max_chars]; truncated = True
    return {
        "info": {
            "path": info.path, "rel": info.rel, "name": info.name,
            "ext": info.ext, "size_bytes": info.size_bytes, "mime": info.mime,
        },
        "truncated": truncated, "note": note, "content": text,
    }




def read_directory_tree(
    root: str, max_files: int = 120, max_chars_per_file: int = 20_000,
    include: Optional[List[str]] = None, exclude: Optional[List[str]] = None,
) -> Dict[str, Any]:
    if not os.path.isdir(root):
        raise NotADirectoryError(f"Not a directory: {root}")
    abs_root = os.path.abspath(root)
    include = include or []
    exclude = (exclude or []) + DEFAULT_EXCLUDES

    def _want(rel: str) -> bool:
        parts = rel.split(os.sep)
        if any(p in exclude for p in parts): return False
        if include and any(fnmatch.fnmatch(rel, pat) for pat in include): return True
        return True

    selected, skipped = [], []
    for dirpath, dirnames, filenames in os.walk(abs_root):
        dirnames[:] = [d for d in dirnames if d not in exclude]
        for fn in filenames:
            p = os.path.join(dirpath, fn)
            rel = os.path.relpath(p, abs_root)
            if not _want(rel): skipped.append(rel); continue
            if os.path.splitext(fn)[1].lower() in BINARY_LIKE_EXTS:
                skipped.append(rel); continue
            selected.append(p)

    def rank_key(p: str) -> Tuple[int, int]:
        rel = os.path.relpath(p, abs_root).lower()
        name = os.path.basename(rel); ext = os.path.splitext(name)[1]
        pri = 3
        if name in {"readme.md", "readme"}: pri = 0
        elif name in {"pyproject.toml","package.json","requirements.txt","setup.py","poetry.lock","environment.yml"}: pri = 1
        elif ext in {".md",".toml",".yaml",".yml",".json"}: pri = 2
        return (pri, len(rel))

    selected.sort(key=rank_key)
    selected = selected[:max_files]

    files_payload = []
    for p in selected:
        info = _probe_file(p, root=abs_root)
        note = ""
        if info.mime and not info.mime.startswith("text") and info.ext not in TEXTY_EXTS:
            note = f"Warning: non-text MIME detected ({info.mime}). Attempting to read as UTF-8."
        raw = open(p, "rb").read()
        try:
            text = raw.decode("utf-8")
        except UnicodeDecodeError:
            text = raw.decode("latin-1"); note = (note + "" if note else "") + "Fallback decoding with latin-1."
        truncated = False
        if len(text) > max_chars_per_file:
            text = text[:max_chars_per_file]; truncated = True
        files_payload.append({
            "info": {
                "path": info.path, "rel": info.rel, "name": info.name,
                "ext": info.ext, "size_bytes": info.size_bytes, "mime": info.mime,
            },
            "truncated": truncated, "note": note, "content": text,
        })

    return {
        "root": abs_root, "files": files_payload, "skipped": skipped,
        "limits": {"max_files": max_files, "max_chars_per_file": max_chars_per_file},
    }
